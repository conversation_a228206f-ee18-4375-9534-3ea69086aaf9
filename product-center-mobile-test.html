<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>产品中心 - 移动端测试</title>
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.min.css">
  <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.3.4/vue.global.min.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.full.min.js"></script>

  <style>
    body {
      margin: 0;
      background: #f5f6fa;
      color: #222;
      font-size: 14px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    .product-center-header {
      padding: 20px 0 12px 0;
      text-align: center;
      position: relative;
      background: #fff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .product-center-header h1 {
      margin: 0;
      color: #2256a0;
      font-size: 1.5rem;
      font-weight: 600;
    }
    
    .product-list-section {
      max-width: 100%;
      margin: 0 auto;
      padding: 16px 12px 24px 12px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    
    /* 加载状态样式 */
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 0;
      color: #666;
    }
    
    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #e1e4e8;
      border-top: 2px solid #2256a0;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .product-list {
      display: flex;
      flex-direction: column;
      gap: 20px;
      justify-content: flex-start;
      font-family: 'Noto Serif SC', serif;
    }
    
    .product-article {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(34,86,160,0.06);
      padding: 20px 16px 24px 16px;
      width: 100%;
      margin: 0 auto;
      overflow: hidden;
      transition: box-shadow 0.3s ease, transform 0.2s ease;
      position: relative;
    }
    
    .product-article:active {
      transform: translateY(1px);
      box-shadow: 0 1px 6px rgba(34,86,160,0.1);
    }
    
    .product-article-title {
      font-size: 1.2rem;
      font-weight: 700;
      color: #2256a0;
      margin-bottom: 12px;
      line-height: 1.3;
      word-break: break-word;
    }
    
    .product-article-img {
      width: 100%;
      max-width: 100%;
      display: block;
      margin: 0 auto 16px auto;
      border-radius: 8px;
      object-fit: cover;
      background: #f8f9fa;
      min-height: 200px;
    }
    
    .product-article-content {
      color: #333;
      font-size: 0.95rem;
      line-height: 1.6;
      word-break: break-word;
      background: #fff;
      padding: 0;
    }
    
    /* WordPress内容样式优化 */
    .product-article-content h2, 
    .product-article-content h3 {
      color: #2256a0;
      margin: 1.2em 0 0.6em 0;
      font-size: 1.1em;
      font-weight: 600;
      line-height: 1.4;
    }
    
    .product-article-content img {
      max-width: 100%;
      height: auto;
      border-radius: 6px;
      margin: 12px 0;
      display: block;
      background: #f8f9fa;
    }
    
    .product-article-content p {
      margin: 0.8em 0;
      text-align: justify;
    }
    
    .product-article-content ul, 
    .product-article-content ol {
      margin: 0.8em 0 0.8em 1.2em;
      padding-left: 0;
    }
    
    .product-article-content li {
      margin: 0.4em 0;
    }
    
    .product-article-content a {
      color: #2256a0;
      text-decoration: none;
      border-bottom: 1px solid transparent;
      transition: border-color 0.2s ease;
    }
    
    .product-article-content a:hover {
      border-bottom-color: #2256a0;
    }
    
    .el-pagination {
      margin: 20px auto 0;
      justify-content: center;
      display: flex;
      transform: scale(0.85);
    }
    
    /* 响应式设计 */
    @media screen and (max-width: 480px) {
      .product-list-section {
        padding: 12px 8px 20px 8px;
        gap: 16px;
      }
      
      .product-article {
        padding: 16px 12px 20px 12px;
        border-radius: 10px;
      }
      
      .product-article-title {
        font-size: 1.1rem;
        margin-bottom: 10px;
      }
      
      .product-article-content {
        font-size: 0.9rem;
        line-height: 1.5;
      }
      
      .product-article-content h2, 
      .product-article-content h3 {
        font-size: 1.05em;
        margin: 1em 0 0.5em 0;
      }
      
      .product-article-img {
        border-radius: 6px;
        margin-bottom: 12px;
        min-height: 180px;
      }
      
      .el-pagination {
        transform: scale(0.8);
      }
    }
    
    @media screen and (max-width: 360px) {
      .product-list-section {
        padding: 10px 6px 16px 6px;
      }
      
      .product-article {
        padding: 14px 10px 18px 10px;
      }
      
      .product-article-title {
        font-size: 1.05rem;
      }
      
      .product-article-content {
        font-size: 0.85rem;
      }
      
      .product-article-img {
        min-height: 160px;
      }
    }
    
    /* 优化触摸体验 */
    @media (hover: none) and (pointer: coarse) {
      .product-article {
        -webkit-tap-highlight-color: rgba(34, 86, 160, 0.1);
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="product-center-header">
      <h1>产品中心</h1>
    </div>
    <div class="product-list-section">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <span>正在加载产品信息...</span>
      </div>
      
      <!-- 产品列表 -->
      <div v-else>
        <div class="product-list" v-if="products.length > 0">
          <div class="product-article" v-for="item in products" :key="item.id">
            <div class="product-article-title">{{ item.title }}</div>
            <img v-if="item.img" :src="item.img" :alt="item.title" class="product-article-img" loading="lazy">
            <div class="product-article-content" v-html="item.content"></div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-else class="loading-container">
          <span>暂无产品信息</span>
        </div>
        
        <!-- 分页器 -->
        <el-pagination
          v-if="total > pageSize && !loading"
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handlePageChange"
          :hide-on-single-page="true"
        />
      </div>
    </div>
  </div>
  
  <script>
    const { createApp, ref, onMounted } = Vue;
    createApp({
      setup() {
        const products = ref([]);
        const loading = ref(true);
        const total = ref(6);
        const pageSize = 3;
        const currentPage = ref(1);

        // 模拟数据
        const mockProducts = [
          {
            id: 1,
            title: "高性能锂电池储能系统",
            img: "https://via.placeholder.com/400x200/2256a0/ffffff?text=产品1",
            content: `
              <h3>产品特点</h3>
              <p>采用先进的锂电池技术，具有高能量密度、长循环寿命等特点。适用于家庭储能、工商业储能等多种应用场景。</p>
              <ul>
                <li>高能量密度：150Wh/kg以上</li>
                <li>长循环寿命：6000次以上</li>
                <li>快速充放电：1C充放电能力</li>
                <li>安全可靠：多重保护机制</li>
              </ul>
            `
          },
          {
            id: 2,
            title: "智能电池管理系统",
            img: "https://via.placeholder.com/400x200/2256a0/ffffff?text=产品2",
            content: `
              <h3>系统功能</h3>
              <p>集成先进的BMS技术，实现对电池组的精确监控和智能管理，确保电池系统的安全稳定运行。</p>
              <ul>
                <li>实时监控电池状态</li>
                <li>智能均衡充电</li>
                <li>故障诊断与预警</li>
                <li>远程监控与控制</li>
              </ul>
            `
          },
          {
            id: 3,
            title: "模块化储能解决方案",
            img: "https://via.placeholder.com/400x200/2256a0/ffffff?text=产品3",
            content: `
              <h3>解决方案优势</h3>
              <p>采用模块化设计理念，可根据用户需求灵活配置容量，支持并联扩展，满足不同规模的储能需求。</p>
              <ul>
                <li>模块化设计，易于扩展</li>
                <li>标准化接口，兼容性强</li>
                <li>智能化管理，运维简便</li>
                <li>高效节能，绿色环保</li>
              </ul>
            `
          }
        ];

        function fetchProducts() {
          loading.value = true;
          // 模拟API调用延迟
          setTimeout(() => {
            const start = (currentPage.value - 1) * pageSize;
            const end = start + pageSize;
            products.value = mockProducts.slice(start, end);
            loading.value = false;
          }, 1000);
        }

        function handlePageChange(page) {
          if (page !== currentPage.value) {
            currentPage.value = page;
            fetchProducts();
            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
          }
        }

        onMounted(fetchProducts);

        return {
          products,
          loading,
          total,
          pageSize,
          currentPage,
          handlePageChange
        };
      }
    }).use(ElementPlus).mount('#app');
  </script>
</body>
</html>
