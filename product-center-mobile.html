<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>产品中心</title>
  <!-- <link rel="stylesheet" href="https://unpkg.com/element-plus@2.9.11/dist/index.css" /> -->
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.min.css">
  <link rel="stylesheet" href="/wp-content/themes/astra/style.css" />
  <link rel="stylesheet" href="/wp-content/plugins/ultimate-addons-for-gutenberg/dist/blocks.style.css">
  <!-- <link rel="stylesheet" href="/wp-content/uploads/uag-plugin/assets/1000/uag-css-915.css"> -->

  <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.3.4/vue.global.min.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.full.min.js"></script>
  <!-- <script src="https://unpkg.com/element-plus@2.9.11"></script> -->

  <style>
    body {
      margin: 0;
      background: #f5f6fa;
      color: #222;
      font-size: 14px;
      /* font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .product-center-header {
      padding: 20px 0 12px 0;
      text-align: center;
      position: relative;
    }

    .product-list-section {
      max-width: 100%;
      margin: 0 auto;
      padding: 16px 12px 24px 12px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    /* 加载状态样式 */
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 0;
      color: #666;
    }

    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #e1e4e8;
      border-top: 2px solid #2256a0;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .product-list {
      display: flex !important;
      flex-direction: column;
      gap: 20px;
      justify-content: flex-start;
      font-family: 'Noto Serif SC', serif;
      visibility: visible !important;
      opacity: 1 !important;
    }

    .product-article {
      background: #fff !important;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(34,86,160,0.06);
      padding: 20px 16px 24px 16px;
      width: 100%;
      margin: 0 auto;
      overflow: hidden;
      transition: box-shadow 0.3s ease, transform 0.2s ease;
      position: relative;
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
      min-height: 100px;
      border: 1px solid #ddd;
    }

    .product-article:active {
      transform: translateY(1px);
      box-shadow: 0 1px 6px rgba(34,86,160,0.1);
    }

    .product-article-title {
      font-size: 1.2rem;
      font-weight: 700;
      color: #2256a0;
      margin-bottom: 12px;
      line-height: 1.3;
      word-break: break-word;
    }

    .product-article-img {
      width: 100%;
      max-width: 100%;
      display: block;
      margin: 0 auto 16px auto;
      border-radius: 8px;
      object-fit: cover;
      background: #f8f9fa;
      min-height: 200px;
    }

    .product-article-content {
      color: #333;
      font-size: 0.95rem;
      line-height: 1.6;
      word-break: break-word;
      background: #fff;
      padding: 0;
    }

    /* WordPress内容样式优化 */
    .product-article-content h2,
    .product-article-content h3 {
      color: #2256a0;
      margin: 1.2em 0 0.6em 0;
      font-size: 1.1em;
      font-weight: 600;
      line-height: 1.4;
    }

    .product-article-content img {
      max-width: 100%;
      height: auto;
      border-radius: 6px;
      margin: 12px 0;
      display: block;
      background: #f8f9fa;
    }

    .product-article-content p {
      margin: 0.8em 0;
      text-align: justify;
    }

    .product-article-content ul,
    .product-article-content ol {
      margin: 0.8em 0 0.8em 1.2em;
      padding-left: 0;
    }

    .product-article-content li {
      margin: 0.4em 0;
    }

    .product-article-content a {
      color: #2256a0;
      text-decoration: none;
      border-bottom: 1px solid transparent;
      transition: border-color 0.2s ease;
    }

    .product-article-content a:hover {
      border-bottom-color: #2256a0;
    }



    /* 响应式设计 */
    @media screen and (max-width: 480px) {
      .product-list-section {
        padding: 12px 8px 20px 8px;
        gap: 16px;
      }

      .product-article {
        padding: 16px 12px 20px 12px;
        border-radius: 10px;
      }

      .product-article-title {
        font-size: 1.1rem;
        margin-bottom: 10px;
      }

      .product-article-content {
        font-size: 0.9rem;
        line-height: 1.5;
      }

      .product-article-content h2,
      .product-article-content h3 {
        font-size: 1.05em;
        margin: 1em 0 0.5em 0;
      }

      .product-article-img {
        border-radius: 6px;
        margin-bottom: 12px;
        min-height: 180px;
      }
    }

    @media screen and (max-width: 360px) {
      .product-list-section {
        padding: 10px 6px 16px 6px;
      }

      .product-article {
        padding: 14px 10px 18px 10px;
      }

      .product-article-title {
        font-size: 1.05rem;
      }

      .product-article-content {
        font-size: 0.85rem;
      }

      .product-article-img {
        min-height: 160px;
      }
    }

    /* 优化触摸体验 */
    @media (hover: none) and (pointer: coarse) {
      .product-article {
        -webkit-tap-highlight-color: rgba(34, 86, 160, 0.1);
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="product-list-section">
      <!-- 调试信息 -->
      <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px;">
        <div>Loading: {{ loading }}</div>
        <div>Products Length: {{ products.length }}</div>
        <div>Error: {{ error }}</div>
        <div v-show="hasProducts">First Product Title: {{ firstProductTitle }}</div>
        <div v-show="hasProducts">First Product Content Length: {{ firstProductContentLength }}</div>
      </div>

      <!-- 加载状态 -->
      <div v-show="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <span>正在加载产品信息...</span>
      </div>

      <!-- 产品列表 -->
      <div v-show="showProducts" class="product-list">
        <div v-for="(item, index) in products" :key="item.id" class="product-article">
          <div style="background: #e3f2fd; padding: 8px; margin-bottom: 8px; border-radius: 4px; font-size: 11px;">
            产品 {{ index + 1 }}: {{ item.title }} (ID: {{ item.id }})
          </div>
          <div class="product-article-content" v-html="item.content"></div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-show="showEmpty" class="loading-container">
        <span>暂无产品信息</span>
      </div>
    </div>
  </div>
  <script>
    const { createApp, ref, onMounted, nextTick, computed } = Vue;
    createApp({
      setup() {
        const tagId = 7;
        const products = ref([]);
        const loading = ref(true);
        const error = ref(null);

        // 计算属性，避免复杂的条件表达式
        const hasProducts = computed(() => products.value.length > 0);
        const showProducts = computed(() => !loading.value && products.value.length > 0);
        const showEmpty = computed(() => !loading.value && products.value.length === 0);
        const firstProductTitle = computed(() => products.value.length > 0 ? products.value[0].title : '');
        const firstProductContentLength = computed(() => products.value.length > 0 && products.value[0].content ? products.value[0].content.length : 0);

        async function fetchProducts() {
          loading.value = true;
          error.value = null;

          try {
            console.log('开始获取产品数据...');
            // 获取所有产品，不分页（per_page=100 获取足够多的产品）
            const response = await fetch(`/wp-json/wp/v2/posts?_embed&per_page=100&tags=${tagId}`);

            console.log('Response status:', response.status);

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Fetched products:', data);
            console.log('Products count:', data.length);

            products.value = data.map((post, index) => {
              console.log(`Processing product ${index + 1}:`, post.title?.rendered);

              let img = '';
              if (post._embedded && post._embedded['wp:featuredmedia'] && post._embedded['wp:featuredmedia'][0]) {
                img = post._embedded['wp:featuredmedia'][0].source_url;
              }

              return {
                id: post.id,
                img: img || '',
                title: post.title && post.title.rendered ? post.title.rendered : '无标题',
                content: post.content && post.content.rendered ? post.content.rendered : ''
              };
            });

            console.log('Processed products:', products.value);
            console.log('Final products length:', products.value.length);

          } catch (e) {
            console.error('Error fetching products:', e);
            error.value = e.message;
            products.value = [];
          } finally {
            loading.value = false;
          }
        }

        // 图片懒加载优化
        function optimizeImages() {
          nextTick(() => {
            const images = document.querySelectorAll('.product-article-content img');
            images.forEach(img => {
              img.loading = 'lazy';
              img.onerror = function() {
                this.style.display = 'none';
              };
            });
          });
        }

        onMounted(() => {
          fetchProducts().then(() => {
            optimizeImages();
          });
        });

        return {
          products,
          loading,
          error,
          hasProducts,
          showProducts,
          showEmpty,
          firstProductTitle,
          firstProductContentLength
        };
      }
    }).use(ElementPlus).mount('#app');
  </script>
</body>
</html>