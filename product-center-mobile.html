<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>产品中心</title>
  <!-- <link rel="stylesheet" href="https://unpkg.com/element-plus@2.9.11/dist/index.css" /> -->
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.min.css">
  <link rel="stylesheet" href="/wp-content/themes/astra/style.css" />
  <link rel="stylesheet" href="/wp-content/plugins/ultimate-addons-for-gutenberg/dist/blocks.style.css">
  <!-- <link rel="stylesheet" href="/wp-content/uploads/uag-plugin/assets/1000/uag-css-915.css"> -->

  <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.3.4/vue.global.min.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.full.min.js"></script>
  <!-- <script src="https://unpkg.com/element-plus@2.9.11"></script> -->

  <style>
    body {
      margin: 0;
      background: #f5f6fa;
      color: #222;
      font-size: 14px; /* 移动端基础字体大小 */
    }
    .product-center-header {
      padding: 20px 0 12px 0; /* 减小头部padding */
      text-align: center;
      position: relative;
    }
    .product-list-section {
      max-width: 100%; /* 移动端宽度占满 */
      margin: 0 auto;
      padding: 16px 12px 24px 12px; /* 调整内边距 */
      display: flex;
      flex-direction: column;
      gap: 20px; /* 减小间距 */
    }
    .product-list {
      display: flex;
      flex-direction: column;
      gap: 24px; /* 减小产品卡片间距 */
      justify-content: flex-start;
      font-family: 'Noto Serif SC';
    }
    .product-article {
      background: #fff;
      border-radius: 8px; /* 减小圆角 */
      box-shadow: 0 2px 8px rgba(34,86,160,0.08); /* 减小阴影 */
      padding: 20px 16px 24px 16px; /* 减小内边距 */
      width: 100%;
      margin: 0 auto;
      overflow: hidden;
    }
    .product-article-title {
      font-size: 1.25rem; /* 标题字号调小 */
      font-weight: 700;
      color: #2256a0;
      margin-bottom: 12px;
      line-height: 1.2;
    }
    .product-article-img {
      width: 100%;
      max-width: 100%; /* 图片宽度占满容器 */
      display: block;
      margin: 0 auto 16px auto;
      border-radius: 6px;
      object-fit: cover;
    }
    .product-article-content {
      color: #222;
      font-size: 1rem; /* 内容字号调小 */
      line-height: 1.6;
      word-break: break-all;
      background: #fff;
      padding: 0;
    }
    /* 适配WordPress内容常用样式 */
    .product-article-content h2, .product-article-content h3 {
      color: #2256a0;
      margin: 1em 0 0.5em 0;
      font-size: 1.2em; /* 调小标题字号 */
    }
    .product-article-content img {
      max-width: 100%;
      border-radius: 4px;
      margin: 12px 0;
      display: block;
    }
    .product-article-content p {
      margin: 0.6em 0;
    }
    .product-article-content ul, .product-article-content ol {
      margin: 0.8em 0 0.8em 1.5em; /* 减小列表缩进 */
    }

    /* 添加移动端优化样式 */
    @media screen and (max-width: 480px) {
      .product-article-content {
        font-size: 0.95rem;
      }
      .product-list-section {
        padding: 12px 8px 20px 8px;
      }
      .product-article {
        padding: 16px 12px 20px 12px;
      }
    }

    @media screen and (max-width: 360px) {
      .product-list-section {
        padding: 10px 6px 16px 6px;
      }
      .product-article {
        padding: 14px 10px 18px 10px;
      }
      .product-article-title {
        font-size: 1.1rem;
      }
      .product-article-content {
        font-size: 0.9rem;
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- <div class="product-center-header">
      <h1>产品中心</h1>
    </div> -->
    <div class="product-list-section">
      <!-- <el-skeleton v-if="loading" rows="6" animated style="width:100%" /> -->
      <div>
        <div class="product-list">
          <div class="product-article" v-for="item in products" :key="item.id">
            <link
              :rel="'stylesheet'"
              :href="`/wp-content/uploads/uag-plugin/assets/1000/uag-css-${item.id}.css`"
            />
            <div class="product-article-content" v-html="item.content"></div>
          </div>
        </div>
        <!-- <el-pagination
          v-if="total > pageSize"
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          @current-change="handlePageChange"
          style="margin-top:32px;"
        /> -->
      </div>
    </div>
  </div>
  <script>
    const { createApp, ref, onMounted } = Vue;
    createApp({
      setup() {
        const tagId = 7;
        const products = ref([]);
        const loading = ref(true);
        const total = ref(0);
        const pageSize = 100; // 获取所有产品，不分页
        const currentPage = ref(1);

        async function fetchProducts() {
          loading.value = true;
          try {
            const response = await fetch(`/wp-json/wp/v2/posts?_embed&per_page=${pageSize}&page=${currentPage.value}&tags=${tagId}`);
            const data = await response.json();
            console.log(data);
            // 获取总数
            const totalCount = response.headers.get('X-WP-Total');
            total.value = totalCount ? parseInt(totalCount) : 0;
            products.value = data.map(post => {
              let img = '';
              if (post._embedded && post._embedded['wp:featuredmedia'] && post._embedded['wp:featuredmedia'][0]) {
                img = post._embedded['wp:featuredmedia'][0].source_url;
              }
              return {
                id: post.id,
                img: img || '',
                title: post.title && post.title.rendered ? post.title.rendered : '无标题',
                content: post.content && post.content.rendered ? post.content.rendered : ''
              };
            });
          } catch (e) {
            products.value = [];
            total.value = 0;
          } finally {
            loading.value = false;
          }
        }

        function handlePageChange(page) {
          currentPage.value = page;
          fetchProducts();
        }

        onMounted(fetchProducts);
        // watch(currentPage, () => fetchProducts());

        return {
          products,
          loading,
          total,
          pageSize,
          currentPage,
          handlePageChange
        };
      }
    }).use(ElementPlus).mount('#app');
  </script>
</body>
</html>